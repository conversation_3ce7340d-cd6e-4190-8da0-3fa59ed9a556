# MCP Interactive Feedback 规则

## 核心规则
1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。
5. 完成任务前，必须使用 MCP mcp-feedback-enhanced。

## 实施指导

### 何时调用 MCP mcp-feedback-enhanced
- 开始任何新任务或流程时
- 完成每个重要步骤后
- 收到用户反馈后（如果反馈内容非空）
- 任务完成前的最终确认
- 遇到问题或需要澄清时

### 调用参数设置
使用以下参数调用 interactive_feedback 工具：
- project_directory: 当前项目目录路径
- summary: 详细描述已完成的工作和当前状态
- timeout: 合理的等待时间（建议 300-600 秒）

### 处理反馈的原则
- 仔细分析用户提供的反馈内容
- 根据反馈调整后续行为和方法
- 如果反馈要求修改或改进，立即执行
- 如果反馈表示满意，继续下一步但仍需再次调用工具

### 终止条件
只有在以下情况下才停止调用 MCP mcp-feedback-enhanced：
- 用户明确说「结束」
- 用户明确说「不再需要交互」
- 用户明确表达类似的终止意图

### 注意事项
- 每次调用都应提供有意义的 summary
- 保持耐心等待用户反馈
- 不要假设用户的意图，通过反馈确认
- 持续改进工作质量直到获得用户满意的反馈
