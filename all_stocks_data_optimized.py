import tushare as ts
import pandas as pd
import time
import logging
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import threading
from time_config import TimeConfig, RateLimitManager

# 配置日志
os.makedirs('data', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('data', 'optimized_data_fetch.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedTushareDataFetcher:
    def __init__(self, token: str):
        """
        优化的数据获取器 - 使用批量查询和失败重试机制
        :param token: tushare token
        """
        self.token = token
        ts.set_token(token)
        self.pro = ts.pro_api()
        
        # 失败请求记录
        self.failed_requests = []
        self.retry_count = 0
        self.max_retries = 3
        
        logger.info("OptimizedTushareDataFetcher initialized")
    
    def _safe_api_call(self, api_func, params: Dict[str, Any], request_name: str, api_name: str) -> Optional[pd.DataFrame]:
        """
        安全的API调用，包含错误处理和记录
        :param api_func: API函数
        :param params: 参数字典
        :param request_name: 请求名称（用于记录）
        :param api_name: API名称字符串
        """
        try:
            TimeConfig.sleep_with_delay('base')  # 使用统一的基础延时
            result = api_func(**params)
            logger.info(f"✓ {request_name}: Success, got {len(result)} records")
            return result

        except Exception as e:
            error_info = {
                'request_name': request_name,
                'api_func_name': api_name,
                'params': params,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.failed_requests.append(error_info)
            logger.error(f"✗ {request_name}: Failed - {e}")

            # 如果是频率限制错误，增加延时
            if any(keyword in str(e).lower() for keyword in ['频率', 'limit', 'rate']):
                logger.warning("Rate limit detected, increasing delay...")
                TimeConfig.sleep_with_delay('rate_limit')

            return None
    
    def get_cashflow_data_batch(self, periods: List[str] = None) -> pd.DataFrame:
        """
        批量获取现金流量数据 - 使用cashflow_vip接口按报告期获取
        """
        logger.info("Starting batch cashflow data collection using VIP interface...")

        if periods is None:
            # 生成最近几个报告期
            periods = self._generate_periods()

        all_data = []

        for period in periods:
            request_name = f"cashflow_vip_batch_{period}"

            # 使用cashflow_vip接口，支持批量查询
            params = {
                "period": period,
                "fields": ["ts_code", "end_date", "net_profit", "n_cashflow_act"]
            }

            df = self._safe_api_call(self.pro.cashflow_vip, params, request_name, "cashflow_vip")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Cashflow VIP batch collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()

    def get_balancesheet_data_batch(self, periods: List[str] = None) -> pd.DataFrame:
        """
        批量获取资产负债表数据 - 使用balancesheet_vip接口按报告期获取
        """
        logger.info("Starting batch balancesheet data collection using VIP interface...")

        if periods is None:
            # 生成最近几个报告期
            periods = self._generate_periods()

        all_data = []

        for period in periods:
            request_name = f"balancesheet_vip_batch_{period}"

            # 使用balancesheet_vip接口，支持批量查询
            params = {
                "period": period,
                "fields": ["ts_code", "end_date", "total_assets", "total_liab", "total_hldr_eqy_exc_min_int"]
            }

            df = self._safe_api_call(self.pro.balancesheet_vip, params, request_name, "balancesheet_vip")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Balancesheet VIP batch collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()
    
    def get_daily_basic_data_batch(self, trade_dates: List[str] = None) -> pd.DataFrame:
        """
        批量获取每日基本面数据
        """
        logger.info("Starting batch daily basic data collection...")

        if trade_dates is None:
            # 获取最近10个交易日
            trade_dates = self._get_recent_trade_dates(10)

        all_data = []

        for trade_date in trade_dates:
            request_name = f"daily_basic_batch_{trade_date}"

            # 使用简化的参数格式
            params = {
                "trade_date": trade_date,
                "fields": ["ts_code", "trade_date", "circ_mv", "total_mv"]
            }

            df = self._safe_api_call(self.pro.daily_basic, params, request_name, "daily_basic")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Daily basic batch collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()
    
    def get_moneyflow_data_batch(self, trade_dates: List[str] = None) -> pd.DataFrame:
        """
        批量获取资金流向数据
        """
        logger.info("Starting batch moneyflow data collection...")

        if trade_dates is None:
            # 获取最近5个交易日
            trade_dates = self._get_recent_trade_dates(5)

        all_data = []

        for trade_date in trade_dates:
            request_name = f"moneyflow_batch_{trade_date}"

            # 使用简化的参数格式
            params = {
                "trade_date": trade_date,
                "fields": [
                    "ts_code", "trade_date", "buy_sm_vol", "buy_sm_amount",
                    "sell_sm_vol", "sell_sm_amount", "buy_md_vol", "buy_md_amount",
                    "sell_md_vol", "sell_md_amount", "buy_lg_vol", "buy_lg_amount",
                    "sell_lg_vol", "sell_lg_amount", "buy_elg_vol", "buy_elg_amount",
                    "sell_elg_vol", "sell_elg_amount", "net_mf_vol", "net_mf_amount",
                    "trade_count"
                ]
            }

            df = self._safe_api_call(self.pro.moneyflow, params, request_name, "moneyflow")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Moneyflow batch collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()
    
    def retry_failed_requests(self) -> Dict[str, pd.DataFrame]:
        """
        重试失败的请求
        """
        if not self.failed_requests:
            logger.info("No failed requests to retry")
            return {}
        
        logger.info(f"Retrying {len(self.failed_requests)} failed requests...")
        retry_results = {}
        remaining_failures = []
        
        for request_info in self.failed_requests:
            try:
                api_func_name = request_info['api_func_name']
                params = request_info['params']
                request_name = request_info['request_name']
                
                # 获取对应的API函数
                api_func = getattr(self.pro, api_func_name)
                
                logger.info(f"Retrying: {request_name}")
                time.sleep(1)  # 重试时增加延时
                
                result = api_func(**params)
                if not result.empty:
                    retry_results[request_name] = result
                    logger.info(f"✓ Retry success: {request_name}, got {len(result)} records")
                else:
                    logger.warning(f"Retry returned empty data: {request_name}")
                    
            except Exception as e:
                logger.error(f"✗ Retry failed: {request_name} - {e}")
                remaining_failures.append(request_info)
        
        # 更新失败请求列表
        self.failed_requests = remaining_failures
        
        logger.info(f"Retry completed: {len(retry_results)} successful, {len(remaining_failures)} still failed")
        return retry_results
    
    def _generate_periods(self) -> List[str]:
        """生成报告期列表 (YYYYMMDD格式)"""
        return TimeConfig.get_quarterly_periods()
    
    def _get_recent_trade_dates(self, days: int) -> List[str]:
        """获取最近的交易日期"""
        return TimeConfig.get_recent_trade_dates(days)
    
    def save_data(self, data: pd.DataFrame, filename: str):
        """保存数据到CSV文件"""
        if not data.empty:
            os.makedirs('data', exist_ok=True)
            filepath = os.path.join('data', f"{filename}.csv")

            data.to_csv(filepath, index=False, encoding='utf-8-sig')

            logger.info(f"Data saved to {filepath}")
            logger.info(f"Data shape: {data.shape}")
        else:
            logger.warning(f"No data to save for {filename}")
    
    def save_failed_requests_log(self):
        """保存失败请求日志"""
        if self.failed_requests:
            os.makedirs('data', exist_ok=True)
            timestamp = TimeConfig.get_current_date_str('timestamp')
            log_file = os.path.join('data', f'failed_requests_{timestamp}.json')

            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_requests, f, ensure_ascii=False, indent=2)

            logger.info(f"Failed requests log saved to {log_file}")

def main():
    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    
    # 初始化优化的数据获取器
    fetcher = OptimizedTushareDataFetcher(token)
    
    try:
        logger.info("=== Starting optimized data collection ===")
        
        # 1. 获取现金流量数据
        logger.info("\n--- Phase 1: Cashflow Data ---")
        cashflow_data = fetcher.get_cashflow_data_batch()
        fetcher.save_data(cashflow_data, 'cashflow_data_optimized')

        # 2. 获取资产负债表数据
        logger.info("\n--- Phase 2: Balancesheet Data ---")
        balancesheet_data = fetcher.get_balancesheet_data_batch()
        fetcher.save_data(balancesheet_data, 'balancesheet_data_optimized')

        # 3. 获取每日基本面数据
        logger.info("\n--- Phase 3: Daily Basic Data ---")
        daily_basic_data = fetcher.get_daily_basic_data_batch()
        fetcher.save_data(daily_basic_data, 'daily_basic_data_optimized')

        # 4. 获取资金流向数据
        logger.info("\n--- Phase 4: Moneyflow Data ---")
        moneyflow_data = fetcher.get_moneyflow_data_batch()
        fetcher.save_data(moneyflow_data, 'moneyflow_data_optimized')
        
        # 5. 重试失败的请求
        logger.info("\n--- Phase 5: Retry Failed Requests ---")
        retry_results = fetcher.retry_failed_requests()
        
        # 保存重试结果
        for name, data in retry_results.items():
            fetcher.save_data(data, f'retry_{name}')
        
        # 6. 如果还有失败的请求，继续重试
        max_retry_rounds = 3
        retry_round = 1

        while fetcher.failed_requests and retry_round <= max_retry_rounds:
            logger.info(f"\n--- Retry Round {retry_round} ---")
            retry_results = fetcher.retry_failed_requests()

            for name, data in retry_results.items():
                fetcher.save_data(data, f'retry_round_{retry_round}_{name}')

            retry_round += 1
        
        # 保存最终的失败请求日志
        fetcher.save_failed_requests_log()
        
        logger.info("=== Data collection completed ===")
        if fetcher.failed_requests:
            logger.warning(f"Still have {len(fetcher.failed_requests)} failed requests")
        else:
            logger.info("All requests completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        fetcher.save_failed_requests_log()
        raise

if __name__ == "__main__":
    main()
