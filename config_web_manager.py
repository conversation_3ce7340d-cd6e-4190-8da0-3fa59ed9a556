"""
时间配置管理Web界面
提供网页形式的配置修改功能
"""

import os
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for
import webbrowser
import threading
import time
from time_config import TimeConfig

app = Flask(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_file = 'time_config.py'
        self.backup_file = 'time_config_backup.py'
    
    def get_current_config(self):
        """获取当前配置"""
        return {
            'DEFAULT_START_YEAR': TimeConfig.DEFAULT_START_YEAR,
            'REPORT_PERIODS_YEARS_BACK': TimeConfig.REPORT_PERIODS_YEARS_BACK,
            'DEFAULT_TRADE_DAYS_COUNT': TimeConfig.DEFAULT_TRADE_DAYS_COUNT,
            'ANNUAL_DATA_YEAR': TimeConfig.ANNUAL_DATA_YEAR,
            'ENABLE_ANNUAL_MODE': TimeConfig.ENABLE_ANNUAL_MODE,
            'BASE_DELAY': TimeConfig.BASE_DELAY,
            'QUICK_DELAY': TimeConfig.QUICK_DELAY,
            'RATE_LIMIT_DELAY': TimeConfig.RATE_LIMIT_DELAY,
            'API_ERROR_DELAY': TimeConfig.API_ERROR_DELAY,
            'MAX_REQUESTS_PER_MINUTE': TimeConfig.MAX_REQUESTS_PER_MINUTE,
            'RATE_LIMIT_WINDOW': TimeConfig.RATE_LIMIT_WINDOW,
            'TEST_DATE': TimeConfig.TEST_DATE
        }
    
    def backup_config(self):
        """备份当前配置"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(self.backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        return False
    
    def update_config(self, new_config):
        """更新配置文件"""
        try:
            # 先备份
            self.backup_config()
            
            # 读取当前配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换配置值
            replacements = {
                'DEFAULT_START_YEAR = 2024': f'DEFAULT_START_YEAR = {new_config["DEFAULT_START_YEAR"]}',
                'REPORT_PERIODS_YEARS_BACK = 1': f'REPORT_PERIODS_YEARS_BACK = {new_config["REPORT_PERIODS_YEARS_BACK"]}',
                'DEFAULT_TRADE_DAYS_COUNT = 10': f'DEFAULT_TRADE_DAYS_COUNT = {new_config["DEFAULT_TRADE_DAYS_COUNT"]}',
                'ANNUAL_DATA_YEAR = 2024': f'ANNUAL_DATA_YEAR = {new_config["ANNUAL_DATA_YEAR"]}',
                'ENABLE_ANNUAL_MODE = False': f'ENABLE_ANNUAL_MODE = {new_config["ENABLE_ANNUAL_MODE"]}',
                'BASE_DELAY = 0.2': f'BASE_DELAY = {new_config["BASE_DELAY"]}',
                'QUICK_DELAY = 0.1': f'QUICK_DELAY = {new_config["QUICK_DELAY"]}',
                'RATE_LIMIT_DELAY = 5': f'RATE_LIMIT_DELAY = {new_config["RATE_LIMIT_DELAY"]}',
                'API_ERROR_DELAY = 60': f'API_ERROR_DELAY = {new_config["API_ERROR_DELAY"]}',
                'MAX_REQUESTS_PER_MINUTE = 100': f'MAX_REQUESTS_PER_MINUTE = {new_config["MAX_REQUESTS_PER_MINUTE"]}',
                'RATE_LIMIT_WINDOW = 60': f'RATE_LIMIT_WINDOW = {new_config["RATE_LIMIT_WINDOW"]}',
                f'TEST_DATE = "{TimeConfig.TEST_DATE}"': f'TEST_DATE = "{new_config["TEST_DATE"]}"'
            }
            
            for old, new in replacements.items():
                content = content.replace(old, new)
            
            # 写入新配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True, "配置更新成功！"
        
        except Exception as e:
            return False, f"配置更新失败：{str(e)}"
    
    def restore_backup(self):
        """恢复备份配置"""
        try:
            if os.path.exists(self.backup_file):
                with open(self.backup_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True, "配置已恢复到备份版本！"
            else:
                return False, "没有找到备份文件！"
        except Exception as e:
            return False, f"恢复配置失败：{str(e)}"

config_manager = ConfigManager()

@app.route('/')
def index():
    """主页"""
    current_config = config_manager.get_current_config()
    return render_template('config_manager.html', config=current_config)

@app.route('/update_config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        new_config = {
            'DEFAULT_START_YEAR': int(request.form['DEFAULT_START_YEAR']),
            'REPORT_PERIODS_YEARS_BACK': int(request.form['REPORT_PERIODS_YEARS_BACK']),
            'DEFAULT_TRADE_DAYS_COUNT': int(request.form['DEFAULT_TRADE_DAYS_COUNT']),
            'ANNUAL_DATA_YEAR': int(request.form['ANNUAL_DATA_YEAR']),
            'ENABLE_ANNUAL_MODE': request.form.get('ENABLE_ANNUAL_MODE') == 'on',
            'BASE_DELAY': float(request.form['BASE_DELAY']),
            'QUICK_DELAY': float(request.form['QUICK_DELAY']),
            'RATE_LIMIT_DELAY': int(request.form['RATE_LIMIT_DELAY']),
            'API_ERROR_DELAY': int(request.form['API_ERROR_DELAY']),
            'MAX_REQUESTS_PER_MINUTE': int(request.form['MAX_REQUESTS_PER_MINUTE']),
            'RATE_LIMIT_WINDOW': int(request.form['RATE_LIMIT_WINDOW']),
            'TEST_DATE': request.form['TEST_DATE']
        }
        
        success, message = config_manager.update_config(new_config)
        
        return jsonify({
            'success': success,
            'message': message
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        })

@app.route('/restore_backup', methods=['POST'])
def restore_backup():
    """恢复备份"""
    success, message = config_manager.restore_backup()
    return jsonify({
        'success': success,
        'message': message
    })

@app.route('/get_current_config')
def get_current_config():
    """获取当前配置（用于刷新页面）"""
    # 重新导入模块以获取最新配置
    import importlib
    import time_config
    importlib.reload(time_config)

    current_config = config_manager.get_current_config()
    return jsonify(current_config)

@app.route('/get_annual_trade_days/<int:year>')
def get_annual_trade_days(year):
    """获取指定年份的交易日数量"""
    try:
        trade_days_count = TimeConfig.get_annual_trade_dates_count(year)
        trade_dates = TimeConfig.get_annual_trade_dates(year)

        return jsonify({
            'success': True,
            'year': year,
            'trade_days_count': trade_days_count,
            'first_trade_date': trade_dates[0] if trade_dates else None,
            'last_trade_date': trade_dates[-1] if trade_dates else None
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def create_html_template():
    """创建HTML模板文件"""
    template_dir = 'templates'
    if not os.path.exists(template_dir):
        os.makedirs(template_dir)
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间配置管理器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-col {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="number"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="number"]:focus, input[type="text"]:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        .section {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .button-group {
            text-align: center;
            margin-top: 30px;
        }
        button {
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .checkbox-container input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        .annual-info {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 14px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕒 时间配置管理器</h1>
        
        <div id="alert" class="alert"></div>
        
        <form id="configForm">
            <div class="section">
                <h3>📅 时间范围设置</h3>
                <div class="form-row">
                    <div class="form-col">
                        <label for="DEFAULT_START_YEAR">默认开始年份:</label>
                        <input type="number" id="DEFAULT_START_YEAR" name="DEFAULT_START_YEAR" 
                               value="{{ config.DEFAULT_START_YEAR }}" min="2020" max="2030">
                        <div class="help-text">数据获取的默认开始年份</div>
                    </div>
                    <div class="form-col">
                        <label for="REPORT_PERIODS_YEARS_BACK">报告期回溯年数:</label>
                        <input type="number" id="REPORT_PERIODS_YEARS_BACK" name="REPORT_PERIODS_YEARS_BACK" 
                               value="{{ config.REPORT_PERIODS_YEARS_BACK }}" min="1" max="5">
                        <div class="help-text">获取最近几年的报告期</div>
                    </div>
                    <div class="form-col">
                        <label for="DEFAULT_TRADE_DAYS_COUNT">默认交易日数量:</label>
                        <input type="number" id="DEFAULT_TRADE_DAYS_COUNT" name="DEFAULT_TRADE_DAYS_COUNT"
                               value="{{ config.DEFAULT_TRADE_DAYS_COUNT }}" min="1" max="500">
                        <div class="help-text">默认获取最近交易日数量（一年约250个交易日）</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <label for="ANNUAL_DATA_YEAR">年度数据年份:</label>
                        <input type="number" id="ANNUAL_DATA_YEAR" name="ANNUAL_DATA_YEAR"
                               value="{{ config.ANNUAL_DATA_YEAR }}" min="2020" max="2030">
                        <div class="help-text">年度数据获取的目标年份</div>
                    </div>
                    <div class="form-col">
                        <div class="checkbox-container">
                            <input type="checkbox" id="ENABLE_ANNUAL_MODE" name="ENABLE_ANNUAL_MODE"
                                   {% if config.ENABLE_ANNUAL_MODE %}checked{% endif %}>
                            <label for="ENABLE_ANNUAL_MODE">启用年度数据模式</label>
                        </div>
                        <div class="help-text">启用后将获取指定年份的所有交易日数据</div>
                        <div id="annual-info" class="annual-info" style="display: none;">
                            <strong>📊 年度数据信息：</strong><br>
                            <span id="annual-trade-days">正在计算...</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>⏱️ 延时设置 (秒)</h3>
                <div class="form-row">
                    <div class="form-col">
                        <label for="BASE_DELAY">基础延时:</label>
                        <input type="number" id="BASE_DELAY" name="BASE_DELAY" 
                               value="{{ config.BASE_DELAY }}" step="0.1" min="0.1" max="5">
                        <div class="help-text">API请求的基础延时</div>
                    </div>
                    <div class="form-col">
                        <label for="QUICK_DELAY">快速延时:</label>
                        <input type="number" id="QUICK_DELAY" name="QUICK_DELAY" 
                               value="{{ config.QUICK_DELAY }}" step="0.1" min="0.05" max="1">
                        <div class="help-text">快速请求的延时</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label for="RATE_LIMIT_DELAY">频率限制延时:</label>
                        <input type="number" id="RATE_LIMIT_DELAY" name="RATE_LIMIT_DELAY" 
                               value="{{ config.RATE_LIMIT_DELAY }}" min="1" max="60">
                        <div class="help-text">触发频率限制时的等待时间</div>
                    </div>
                    <div class="form-col">
                        <label for="API_ERROR_DELAY">API错误延时:</label>
                        <input type="number" id="API_ERROR_DELAY" name="API_ERROR_DELAY" 
                               value="{{ config.API_ERROR_DELAY }}" min="10" max="300">
                        <div class="help-text">API错误时的等待时间</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>🚦 速率限制设置</h3>
                <div class="form-row">
                    <div class="form-col">
                        <label for="MAX_REQUESTS_PER_MINUTE">每分钟最大请求数:</label>
                        <input type="number" id="MAX_REQUESTS_PER_MINUTE" name="MAX_REQUESTS_PER_MINUTE" 
                               value="{{ config.MAX_REQUESTS_PER_MINUTE }}" min="10" max="500">
                        <div class="help-text">速率限制的最大请求数</div>
                    </div>
                    <div class="form-col">
                        <label for="RATE_LIMIT_WINDOW">速率限制时间窗口:</label>
                        <input type="number" id="RATE_LIMIT_WINDOW" name="RATE_LIMIT_WINDOW" 
                               value="{{ config.RATE_LIMIT_WINDOW }}" min="30" max="300">
                        <div class="help-text">速率限制的时间窗口（秒）</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>🧪 测试设置</h3>
                <div class="form-row">
                    <div class="form-col">
                        <label for="TEST_DATE">测试日期 (YYYYMMDD):</label>
                        <input type="text" id="TEST_DATE" name="TEST_DATE" 
                               value="{{ config.TEST_DATE }}" pattern="[0-9]{8}">
                        <div class="help-text">用于测试的固定日期</div>
                    </div>
                </div>
            </div>

            <div class="button-group">
                <button type="submit" class="btn-primary">💾 保存配置</button>
                <button type="button" onclick="restoreBackup()" class="btn-warning">🔄 恢复备份</button>
                <button type="button" onclick="refreshConfig()" class="btn-info">🔄 刷新配置</button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('configForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/update_config', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, data.success ? 'success' : 'danger');
                if (data.success) {
                    setTimeout(() => {
                        refreshConfig();
                    }, 1000);
                }
            })
            .catch(error => {
                showAlert('请求失败: ' + error.message, 'danger');
            });
        });

        function restoreBackup() {
            if (confirm('确定要恢复到备份配置吗？当前配置将被覆盖！')) {
                fetch('/restore_backup', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    showAlert(data.message, data.success ? 'success' : 'danger');
                    if (data.success) {
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    }
                })
                .catch(error => {
                    showAlert('请求失败: ' + error.message, 'danger');
                });
            }
        }

        function refreshConfig() {
            fetch('/get_current_config')
            .then(response => response.json())
            .then(config => {
                // 更新表单值
                for (const [key, value] of Object.entries(config)) {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = value;
                    }
                }
                showAlert('配置已刷新！', 'success');
            })
            .catch(error => {
                showAlert('刷新失败: ' + error.message, 'danger');
            });
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';

            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }

        // 年度数据模式相关功能
        function updateAnnualInfo() {
            const enableAnnual = document.getElementById('ENABLE_ANNUAL_MODE').checked;
            const annualYear = document.getElementById('ANNUAL_DATA_YEAR').value;
            const annualInfo = document.getElementById('annual-info');
            const tradeDaysSpan = document.getElementById('annual-trade-days');

            if (enableAnnual && annualYear) {
                annualInfo.style.display = 'block';
                tradeDaysSpan.innerHTML = '正在计算交易日数量...';

                // 使用API获取准确的交易日信息
                fetch(`/get_annual_trade_days/${annualYear}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        tradeDaysSpan.innerHTML = `
                            <strong>${data.year}年</strong> 共有 <strong style="color: #1976d2;">${data.trade_days_count}</strong> 个交易日<br>
                            <small>📅 首个交易日: ${formatDate(data.first_trade_date)}</small><br>
                            <small>📅 最后交易日: ${formatDate(data.last_trade_date)}</small><br>
                            <small style="color: #ff9800;">⚠️ 启用年度模式后，"默认交易日数量"设置将被忽略</small>
                        `;
                    } else {
                        tradeDaysSpan.innerHTML = `<span style="color: red;">计算失败: ${data.error}</span>`;
                    }
                })
                .catch(error => {
                    tradeDaysSpan.innerHTML = `<span style="color: red;">网络错误: ${error.message}</span>`;
                });
            } else {
                annualInfo.style.display = 'none';
            }
        }

        function formatDate(dateStr) {
            if (!dateStr) return '未知';
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${year}/${month}/${day}`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateAnnualInfo();

            // 监听年度模式复选框变化
            document.getElementById('ENABLE_ANNUAL_MODE').addEventListener('change', updateAnnualInfo);

            // 监听年份输入变化
            document.getElementById('ANNUAL_DATA_YEAR').addEventListener('input', updateAnnualInfo);
        });
    </script>
</body>
</html>'''
    
    with open(os.path.join(template_dir, 'config_manager.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)
    webbrowser.open('http://localhost:5000')

def main():
    """主函数"""
    print("🚀 启动时间配置管理器...")
    
    # 创建HTML模板
    create_html_template()
    
    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    print("📱 Web界面将在浏览器中打开...")
    print("🌐 访问地址: http://localhost:5000")
    print("⚠️  按 Ctrl+C 停止服务器")
    
    # 启动Flask应用
    app.run(debug=False, host='localhost', port=5000)

if __name__ == '__main__':
    main()
