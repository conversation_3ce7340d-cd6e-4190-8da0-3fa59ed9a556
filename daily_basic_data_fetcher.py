import tushare as ts
import pandas as pd
import time
import logging
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from time_config import TimeConfig, RateLimitManager

# 配置日志
os.makedirs('data', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('data', 'daily_basic_data_fetch.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DailyBasicDataFetcher:
    """每日基本面数据获取器"""
    
    def __init__(self, token: str, max_workers: int = 10):
        """
        初始化每日基本面数据获取器
        :param token: tushare token
        :param max_workers: 最大线程数，默认10个
        """
        self.token = token
        ts.set_token(token)
        self.pro = ts.pro_api()

        # 失败请求记录
        self.failed_requests = []
        self.retry_count = 0
        self.max_retries = 3

        # 多线程配置
        self.max_workers = max_workers
        self.thread_lock = threading.Lock()

        # 性能优化配置
        self.batch_size = 50  # 批量处理大小
        self.connection_pool_size = max_workers * 2  # 连接池大小

        # 缓存机制
        self.data_cache = {}  # 简单的内存缓存
        self.cache_enabled = True

        logger.info(f"DailyBasicDataFetcher initialized with {max_workers} workers, batch_size={self.batch_size}, cache_enabled={self.cache_enabled}")
    
    def _safe_api_call(self, api_func, params: Dict[str, Any], request_name: str, api_name: str) -> Optional[pd.DataFrame]:
        """
        安全的API调用，包含错误处理和记录（优化版）
        :param api_func: API函数
        :param params: 参数字典
        :param request_name: 请求名称（用于记录）
        :param api_name: API名称字符串
        """
        try:
            # 使用更快的延时策略
            TimeConfig.sleep_with_delay('quick')  # 使用快速延时而不是基础延时
            result = api_func(**params)
            logger.info(f"✓ {request_name}: Success, got {len(result)} records")
            return result

        except Exception as e:
            error_info = {
                'request_name': request_name,
                'api_func_name': api_name,
                'params': params,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            with self.thread_lock:  # 线程安全地添加失败请求
                self.failed_requests.append(error_info)
            logger.error(f"✗ {request_name}: Failed - {e}")

            # 如果是频率限制错误，增加延时
            if any(keyword in str(e).lower() for keyword in ['频率', 'limit', 'rate']):
                logger.warning("Rate limit detected, increasing delay...")
                TimeConfig.sleep_with_delay('rate_limit')

            return None

    def _fetch_single_date_data(self, trade_date: str, fields: List[str]) -> Optional[pd.DataFrame]:
        """
        获取单个日期的数据（线程安全，支持缓存）
        :param trade_date: 交易日期
        :param fields: 字段列表
        """
        # 生成缓存键
        cache_key = f"daily_basic_{trade_date}_{hash(tuple(sorted(fields)))}"

        # 检查缓存
        if self.cache_enabled and cache_key in self.data_cache:
            logger.info(f"✓ Cache hit for {trade_date}")
            return self.data_cache[cache_key]

        request_name = f"daily_basic_thread_{trade_date}"
        params = {
            "trade_date": trade_date,
            "fields": fields
        }

        result = self._safe_api_call(self.pro.daily_basic, params, request_name, "daily_basic")

        # 缓存结果
        if self.cache_enabled and result is not None:
            with self.thread_lock:
                self.data_cache[cache_key] = result

        return result
    
    def get_daily_basic_data_batch(self, trade_dates: List[str] = None, fields: List[str] = None) -> pd.DataFrame:
        """
        批量获取每日基本面数据
        :param trade_dates: 交易日期列表，格式YYYYMMDD
        :param fields: 需要获取的字段列表
        """
        logger.info("Starting batch daily basic data collection...")

        if trade_dates is None:
            # 获取最近10个交易日
            trade_dates = self._get_recent_trade_dates(10)

        if fields is None:
            # 默认字段
            fields = [
                "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
                "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio",
                "dv_ttm", "total_share", "float_share", "free_share", "total_mv",
                "circ_mv"
            ]

        all_data = []

        for trade_date in trade_dates:
            request_name = f"daily_basic_batch_{trade_date}"

            # 使用简化的参数格式
            params = {
                "trade_date": trade_date,
                "fields": fields
            }

            df = self._safe_api_call(self.pro.daily_basic, params, request_name, "daily_basic")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Daily basic batch collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()

    def get_daily_basic_data_batch_threaded(self, trade_dates: List[str] = None, fields: List[str] = None) -> pd.DataFrame:
        """
        多线程批量获取每日基本面数据
        :param trade_dates: 交易日期列表，格式YYYYMMDD
        :param fields: 需要获取的字段列表
        """
        logger.info("Starting threaded batch daily basic data collection...")

        if trade_dates is None:
            # 获取最近10个交易日
            trade_dates = self._get_recent_trade_dates(10)

        if fields is None:
            # 默认字段
            fields = [
                "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
                "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio",
                "dv_ttm", "total_share", "float_share", "free_share", "total_mv",
                "circ_mv"
            ]

        all_data = []

        # 使用线程池并发获取数据
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_date = {
                executor.submit(self._fetch_single_date_data, trade_date, fields): trade_date
                for trade_date in trade_dates
            }

            # 收集结果
            for future in as_completed(future_to_date):
                trade_date = future_to_date[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        with self.thread_lock:
                            all_data.append(df)
                except Exception as e:
                    logger.error(f"Thread error for date {trade_date}: {e}")

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Threaded daily basic batch collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()

    def get_daily_basic_data_range(self, start_date: str, end_date: str, fields: List[str] = None) -> pd.DataFrame:
        """
        获取指定日期区间的每日基本面数据
        :param start_date: 开始日期，格式YYYYMMDD
        :param end_date: 结束日期，格式YYYYMMDD
        :param fields: 需要获取的字段列表
        """
        logger.info(f"Starting daily basic data collection for range: {start_date} to {end_date}")

        if fields is None:
            # 默认字段
            fields = [
                "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
                "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio",
                "dv_ttm", "total_share", "float_share", "free_share", "total_mv",
                "circ_mv"
            ]

        # 生成日期区间内的交易日
        trade_dates = self._generate_trade_dates_in_range(start_date, end_date)
        logger.info(f"Generated {len(trade_dates)} trade dates in range")

        all_data = []

        for trade_date in trade_dates:
            request_name = f"daily_basic_range_{trade_date}"

            params = {
                "trade_date": trade_date,
                "fields": fields
            }

            df = self._safe_api_call(self.pro.daily_basic, params, request_name, "daily_basic")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Daily basic range collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()

    def get_daily_basic_data_range_threaded(self, start_date: str, end_date: str, fields: List[str] = None) -> pd.DataFrame:
        """
        多线程获取指定日期区间的每日基本面数据
        :param start_date: 开始日期，格式YYYYMMDD
        :param end_date: 结束日期，格式YYYYMMDD
        :param fields: 需要获取的字段列表
        """
        logger.info(f"Starting threaded daily basic data collection for range: {start_date} to {end_date}")

        if fields is None:
            # 默认字段
            fields = [
                "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
                "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio",
                "dv_ttm", "total_share", "float_share", "free_share", "total_mv",
                "circ_mv"
            ]

        # 生成日期区间内的交易日
        trade_dates = self._generate_trade_dates_in_range(start_date, end_date)
        logger.info(f"Generated {len(trade_dates)} trade dates in range")

        all_data = []

        # 使用线程池并发获取数据
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_date = {
                executor.submit(self._fetch_single_date_data, trade_date, fields): trade_date
                for trade_date in trade_dates
            }

            # 收集结果
            for future in as_completed(future_to_date):
                trade_date = future_to_date[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        with self.thread_lock:
                            all_data.append(df)
                except Exception as e:
                    logger.error(f"Thread error for date {trade_date}: {e}")

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Threaded daily basic range collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()
    
    def get_daily_basic_data_by_stock(self, ts_codes: List[str], start_date: str = None, end_date: str = None, fields: List[str] = None) -> pd.DataFrame:
        """
        按股票代码获取每日基本面数据
        :param ts_codes: 股票代码列表
        :param start_date: 开始日期，格式YYYYMMDD
        :param end_date: 结束日期，格式YYYYMMDD
        :param fields: 需要获取的字段列表
        """
        logger.info(f"Starting daily basic data collection for {len(ts_codes)} stocks...")

        if fields is None:
            # 默认字段
            fields = [
                "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
                "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio",
                "dv_ttm", "total_share", "float_share", "free_share", "total_mv",
                "circ_mv"
            ]

        all_data = []

        for ts_code in ts_codes:
            request_name = f"daily_basic_stock_{ts_code}"

            params = {
                "ts_code": ts_code,
                "fields": fields
            }
            
            if start_date:
                params["start_date"] = start_date
            if end_date:
                params["end_date"] = end_date

            df = self._safe_api_call(self.pro.daily_basic, params, request_name, "daily_basic")
            if df is not None and not df.empty:
                all_data.append(df)

        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            logger.info(f"Daily basic stock collection completed: {len(result)} total records")
            return result
        else:
            return pd.DataFrame()
    
    def get_daily_basic_data_single_date(self, trade_date: str, fields: List[str] = None) -> pd.DataFrame:
        """
        获取单个交易日的所有股票基本面数据
        :param trade_date: 交易日期，格式YYYYMMDD
        :param fields: 需要获取的字段列表
        """
        logger.info(f"Getting daily basic data for date: {trade_date}")

        if fields is None:
            # 默认字段
            fields = [
                "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
                "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio",
                "dv_ttm", "total_share", "float_share", "free_share", "total_mv",
                "circ_mv"
            ]

        request_name = f"daily_basic_single_{trade_date}"
        params = {
            "trade_date": trade_date,
            "fields": fields
        }

        df = self._safe_api_call(self.pro.daily_basic, params, request_name, "daily_basic")
        if df is not None and not df.empty:
            logger.info(f"Daily basic single date collection completed: {len(df)} records")
            return df
        else:
            return pd.DataFrame()
    
    def retry_failed_requests(self) -> Dict[str, pd.DataFrame]:
        """
        重试失败的请求
        """
        if not self.failed_requests:
            logger.info("No failed requests to retry")
            return {}
        
        logger.info(f"Retrying {len(self.failed_requests)} failed requests...")
        retry_results = {}
        remaining_failures = []
        
        for request_info in self.failed_requests:
            try:
                api_func_name = request_info['api_func_name']
                params = request_info['params']
                request_name = request_info['request_name']
                
                # 获取对应的API函数
                api_func = getattr(self.pro, api_func_name)
                
                logger.info(f"Retrying: {request_name}")
                time.sleep(1)  # 重试时增加延时
                
                result = api_func(**params)
                if not result.empty:
                    retry_results[request_name] = result
                    logger.info(f"✓ Retry success: {request_name}, got {len(result)} records")
                else:
                    logger.warning(f"Retry returned empty data: {request_name}")
                    
            except Exception as e:
                logger.error(f"✗ Retry failed: {request_name} - {e}")
                remaining_failures.append(request_info)
        
        # 更新失败请求列表
        self.failed_requests = remaining_failures
        
        logger.info(f"Retry completed: {len(retry_results)} successful, {len(remaining_failures)} still failed")
        return retry_results
    
    def _get_recent_trade_dates(self, days: int) -> List[str]:
        """获取最近的交易日期"""
        return TimeConfig.get_recent_trade_dates(days)

    def _generate_trade_dates_in_range(self, start_date: str, end_date: str) -> List[str]:
        """
        生成指定日期区间内的交易日期列表
        :param start_date: 开始日期，格式YYYYMMDD
        :param end_date: 结束日期，格式YYYYMMDD
        :return: 交易日期列表
        """
        from datetime import datetime, timedelta

        try:
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d')
        except ValueError as e:
            logger.error(f"Invalid date format: {e}")
            return []

        if start_dt > end_dt:
            logger.error("Start date must be before or equal to end date")
            return []

        trade_dates = []
        current_dt = start_dt

        while current_dt <= end_dt:
            # 简单过滤周末（周一到周五为交易日）
            if current_dt.weekday() < 5:  # 0-4 表示周一到周五
                date_str = current_dt.strftime('%Y%m%d')
                trade_dates.append(date_str)
            current_dt += timedelta(days=1)

        return trade_dates

    def clear_cache(self):
        """清理缓存"""
        with self.thread_lock:
            self.data_cache.clear()
        logger.info("Cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.thread_lock:
            return {
                'cache_size': len(self.data_cache),
                'cache_enabled': self.cache_enabled,
                'failed_requests': len(self.failed_requests)
            }

    def enable_cache(self, enabled: bool = True):
        """启用或禁用缓存"""
        self.cache_enabled = enabled
        logger.info(f"Cache {'enabled' if enabled else 'disabled'}")

    def optimize_performance(self):
        """性能优化建议"""
        stats = self.get_cache_stats()
        suggestions = []

        if stats['failed_requests'] > 10:
            suggestions.append("考虑减少线程数或增加延时")

        if stats['cache_size'] > 1000:
            suggestions.append("考虑清理缓存以释放内存")

        if not stats['cache_enabled']:
            suggestions.append("启用缓存可以提高重复查询的性能")

        return suggestions
    
    def save_data(self, data: pd.DataFrame, filename: str = None, append_mode: bool = False):
        """
        保存数据到CSV文件
        :param data: 要保存的数据
        :param filename: 文件名，默认为 daily_basic_data
        :param append_mode: 是否追加模式
        """
        if not data.empty:
            os.makedirs('data', exist_ok=True)

            if filename is None:
                filename = "daily_basic_data"

            filepath = os.path.join('data', f"{filename}.csv")

            # 如果是追加模式且文件已存在，则追加数据
            if append_mode and os.path.exists(filepath):
                data.to_csv(filepath, mode='a', header=False, index=False, encoding='utf-8-sig')
                logger.info(f"Data appended to {filepath}")
            else:
                data.to_csv(filepath, index=False, encoding='utf-8-sig')
                logger.info(f"Data saved to {filepath}")

            logger.info(f"Data shape: {data.shape}")
        else:
            logger.warning(f"No data to save for {filename}")
    
    def save_failed_requests_log(self):
        """保存失败请求日志"""
        if self.failed_requests:
            os.makedirs('data', exist_ok=True)
            timestamp = TimeConfig.get_current_date_str('timestamp')
            log_file = os.path.join('data', f'daily_basic_failed_requests_{timestamp}.json')

            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_requests, f, ensure_ascii=False, indent=2)

            logger.info(f"Failed requests log saved to {log_file}")

def get_user_date_input():
    """获取用户输入的日期区间"""
    print("\n=== 每日基本面数据获取器 ===")
    print("请选择数据获取方式：")
    print("1. 获取最近数据（默认最近10个交易日）")
    print("2. 指定日期区间")

    choice = input("请输入选择 (1 或 2，默认为1): ").strip()

    if choice == "2":
        print("\n请输入日期区间（格式：YYYYMMDD）")
        start_date = input("开始日期 (例如: 20241001): ").strip()
        end_date = input("结束日期 (例如: 20241031): ").strip()

        # 验证日期格式
        if len(start_date) == 8 and len(end_date) == 8 and start_date.isdigit() and end_date.isdigit():
            return start_date, end_date
        else:
            print("日期格式错误，使用默认方式获取最近数据")
            return None, None
    else:
        return None, None

def main():
    """主函数 - 获取每日基本面数据"""
    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"

    # 初始化每日基本面数据获取器（10个线程）
    fetcher = DailyBasicDataFetcher(token, max_workers=10)

    try:
        # 获取用户输入的日期区间
        start_date, end_date = get_user_date_input()

        logger.info("=== 开始获取每日基本面数据 ===")

        if start_date and end_date:
            # 使用用户指定的日期区间
            logger.info(f"正在获取日期区间数据: {start_date} 到 {end_date}")
            daily_basic_data = fetcher.get_daily_basic_data_range_threaded(
                start_date=start_date,
                end_date=end_date
            )
        else:
            # 使用默认方式获取最近数据
            logger.info("正在获取最近交易日数据...")
            daily_basic_data = fetcher.get_daily_basic_data_batch_threaded(trade_dates=None)

        # 保存主要文件
        fetcher.save_data(daily_basic_data)  # 保存为 daily_basic_data.csv

        # 重试失败的请求
        retry_results = fetcher.retry_failed_requests()
        if retry_results:
            logger.info(f"重试成功 {len(retry_results)} 个请求")

        # 保存失败请求日志
        fetcher.save_failed_requests_log()

        logger.info("=== 每日基本面数据获取完成 ===")

    except Exception as e:
        logger.error(f"数据获取出错: {e}")
        fetcher.save_failed_requests_log()
        raise

if __name__ == "__main__":
    main()
