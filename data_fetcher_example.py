"""
数据获取器使用示例
展示如何使用优化后的每日基本面数据和资金流向数据模块
"""

import logging
from daily_basic_data_fetcher import DailyBasicDataFetcher
from moneyflow_data_fetcher import MoneyflowDataFetcher
from time_config import TimeConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def example_daily_basic_data(start_date=None, end_date=None):
    """每日基本面数据获取示例"""
    logger.info("=== 每日基本面数据获取示例 ===")

    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"

    # 初始化每日基本面数据获取器（支持多线程）
    fetcher = DailyBasicDataFetcher(token, max_workers=10)

    try:
        if start_date and end_date:
            # 使用用户指定的日期区间
            logger.info(f"--- 获取每日基本面数据: {start_date} 到 {end_date} ---")
            daily_basic_data = fetcher.get_daily_basic_data_range_threaded(
                start_date=start_date,
                end_date=end_date
            )
        else:
            # 使用多线程获取最近数据（推荐的高性能方式）
            logger.info("--- 获取每日基本面数据 ---")
            daily_basic_data = fetcher.get_daily_basic_data_batch_threaded()

        # 保存到默认文件
        fetcher.save_data(daily_basic_data)  # 保存为 daily_basic_data.csv
        
        # 重试失败的请求
        retry_results = fetcher.retry_failed_requests()
        for name, data in retry_results.items():
            fetcher.save_data(data, f'retry_{name}')
        
        # 保存失败请求日志
        fetcher.save_failed_requests_log()
        
        logger.info("每日基本面数据获取示例完成")
        
    except Exception as e:
        logger.error(f"每日基本面数据获取示例出错: {e}")
        fetcher.save_failed_requests_log()

def example_moneyflow_data(start_date=None, end_date=None):
    """资金流向数据获取示例"""
    logger.info("=== 资金流向数据获取示例 ===")

    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"

    # 初始化资金流向数据获取器（支持多线程）
    fetcher = MoneyflowDataFetcher(token, max_workers=10)

    try:
        if start_date and end_date:
            # 使用用户指定的日期区间
            logger.info(f"--- 获取资金流向数据: {start_date} 到 {end_date} ---")
            moneyflow_data = fetcher.get_moneyflow_data_range_threaded(
                start_date=start_date,
                end_date=end_date
            )
        else:
            # 使用多线程获取最近数据（推荐的高性能方式）
            logger.info("--- 获取资金流向数据 ---")
            moneyflow_data = fetcher.get_moneyflow_data_batch_threaded()

        # 保存到默认文件
        fetcher.save_data(moneyflow_data)  # 保存为 moneyflow_data.csv
        
        # 重试失败的请求
        retry_results = fetcher.retry_failed_requests()
        for name, data in retry_results.items():
            fetcher.save_data(data, f'retry_{name}')
        
        # 保存失败请求日志
        fetcher.save_failed_requests_log()
        
        logger.info("资金流向数据获取示例完成")
        
    except Exception as e:
        logger.error(f"资金流向数据获取示例出错: {e}")
        fetcher.save_failed_requests_log()

def example_combined_usage(start_date=None, end_date=None):
    """组合使用示例"""
    logger.info("=== 组合使用示例 ===")

    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"

    # 初始化两个数据获取器（支持多线程）
    daily_fetcher = DailyBasicDataFetcher(token, max_workers=10)
    money_fetcher = MoneyflowDataFetcher(token, max_workers=10)

    try:
        if start_date and end_date:
            # 获取指定日期区间的基本面和资金流向数据
            logger.info(f"--- 获取基本面和资金流向数据: {start_date} 到 {end_date} ---")

            # 获取基本面数据
            daily_data = daily_fetcher.get_daily_basic_data_range_threaded(
                start_date=start_date,
                end_date=end_date
            )

            # 获取资金流向数据
            money_data = money_fetcher.get_moneyflow_data_range_threaded(
                start_date=start_date,
                end_date=end_date
            )
        else:
            # 获取最新的基本面和资金流向数据
            logger.info("--- 获取基本面和资金流向数据 ---")

            # 获取基本面数据
            daily_data = daily_fetcher.get_daily_basic_data_batch_threaded()

            # 获取资金流向数据
            money_data = money_fetcher.get_moneyflow_data_batch_threaded()

        # 保存到默认文件
        daily_fetcher.save_data(daily_data)  # 保存为 daily_basic_data.csv
        money_fetcher.save_data(money_data)  # 保存为 moneyflow_data.csv

        # 数据统计
        if not daily_data.empty and not money_data.empty:
            logger.info(f"基本面数据: {len(daily_data)} 条记录")
            logger.info(f"资金流向数据: {len(money_data)} 条记录")
        
        logger.info("组合使用示例完成")
        
    except Exception as e:
        logger.error(f"组合使用示例出错: {e}")

def get_user_date_input():
    """获取用户输入的日期区间"""
    print("\n=== 数据获取器 ===")
    print("请选择数据获取方式：")
    print("1. 获取最近数据（默认）")
    print("2. 指定日期区间")

    choice = input("请输入选择 (1 或 2，默认为1): ").strip()

    if choice == "2":
        print("\n请输入日期区间（格式：YYYYMMDD）")
        start_date = input("开始日期 (例如: 20241001): ").strip()
        end_date = input("结束日期 (例如: 20241031): ").strip()

        # 验证日期格式
        if len(start_date) == 8 and len(end_date) == 8 and start_date.isdigit() and end_date.isdigit():
            return start_date, end_date
        else:
            print("日期格式错误，使用默认方式获取最近数据")
            return None, None
    else:
        return None, None

def main():
    """主函数 - 运行数据获取示例"""
    logger.info("开始运行数据获取器")

    # 获取用户输入的日期区间
    start_date, end_date = get_user_date_input()

    # 运行每日基本面数据获取
    example_daily_basic_data(start_date, end_date)

    # 运行资金流向数据获取
    example_moneyflow_data(start_date, end_date)

    # 运行组合使用
    example_combined_usage(start_date, end_date)

    logger.info("数据获取完成")

if __name__ == "__main__":
    main()
