import tushare as ts
import pandas as pd
import time
import logging
import os
from typing import List, Dict, Optional
from time_config import TimeConfig, RateLimitManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('index_weight_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IndexWeightFetcher:
    def __init__(self, token: str):
        """
        初始化指数权重数据获取器
        :param token: tushare token
        """
        self.token = token
        ts.set_token(token)
        self.pro = ts.pro_api()

        # 使用统一的速率限制管理器
        self.rate_limiter = RateLimitManager()

        logger.info("IndexWeightFetcher initialized")
    
    def _rate_limit_check(self):
        """检查并控制请求速率"""
        self.rate_limiter.check_and_wait()
    
    def get_month_dates(self, start_year: int = None, end_date: str = None) -> List[str]:
        """
        生成从指定年份到当前的每月最后一个交易日列表
        :param start_year: 开始年份，默认使用配置中的DEFAULT_START_YEAR
        :param end_date: 结束日期，格式YYYYMMDD，默认为当前日期
        """
        return TimeConfig.get_month_end_dates(start_year, end_date)
    
    def get_index_weight_data(self, index_codes: List[str], trade_dates: List[str]) -> pd.DataFrame:
        """
        获取指数权重数据并聚合为股票列表格式
        :param index_codes: 指数代码列表
        :param trade_dates: 交易日期列表
        """
        logger.info(f"Fetching index weight data for {len(index_codes)} indices across {len(trade_dates)} dates")
        all_data = []
        total_requests = len(index_codes) * len(trade_dates)
        current_request = 0

        for index_code in index_codes:
            logger.info(f"Processing index: {index_code}")

            for trade_date in trade_dates:
                try:
                    current_request += 1
                    self._rate_limit_check()

                    logger.info(f"Fetching data for {index_code} on {trade_date} ({current_request}/{total_requests})")

                    df = self.pro.index_weight(
                        index_code=index_code,
                        trade_date=trade_date,
                        fields=[
                            "index_code",
                            "con_code",
                            "trade_date"
                        ]
                    )

                    if not df.empty:
                        # 将该日期该指数的所有股票代码聚合为一个字符串
                        stock_list = ' '.join(df['con_code'].tolist())
                        aggregated_row = {
                            'index_code': index_code,
                            'trade_date': trade_date,
                            'con_codes': stock_list,
                            'stock_count': len(df)
                        }
                        all_data.append(aggregated_row)
                        logger.info(f"Got {len(df)} stocks for {index_code} on {trade_date}")
                    else:
                        logger.warning(f"No data for {index_code} on {trade_date}")

                except Exception as e:
                    logger.error(f"Error fetching data for {index_code} on {trade_date}: {e}")
                    # 如果是API限制错误，增加等待时间
                    if "频率" in str(e) or "limit" in str(e).lower():
                        logger.warning("API rate limit hit, waiting...")
                        TimeConfig.sleep_with_delay('api_error')
                    continue

        if all_data:
            result = pd.DataFrame(all_data)
            # 按日期和指数代码排序
            result = result.sort_values(['trade_date', 'index_code'])
            logger.info(f"Total collected records: {len(result)}")
            return result
        else:
            logger.warning("No data collected")
            return pd.DataFrame()
    
    def save_data(self, data: pd.DataFrame, filename: str):
        """保存数据到CSV文件"""
        if not data.empty:
            # 创建数据目录
            os.makedirs('data', exist_ok=True)
            filepath = os.path.join('data', f"{filename}.csv")
            
            # 保存为CSV格式
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"Data saved to {filepath}")
            logger.info(f"Data shape: {data.shape}")

            # 显示数据概览
            logger.info("Data overview:")
            logger.info(f"Date range: {data['trade_date'].min()} to {data['trade_date'].max()}")
            logger.info(f"Indices: {data['index_code'].unique().tolist()}")
            logger.info(f"Unique dates: {len(data['trade_date'].unique())}")
            logger.info(f"Total stock count range: {data['stock_count'].min()} to {data['stock_count'].max()}")
        else:
            logger.warning(f"No data to save for {filename}")
    
    def test_api_limits(self, index_code: str = "932000.CSI"):
        """
        测试API限制
        :param index_code: 测试用的指数代码
        """
        logger.info("Testing API limits...")
        test_date = TimeConfig.TEST_DATE  # 使用配置中的测试日期
        
        start_time = time.time()
        success_count = 0
        
        for i in range(10):  # 测试10次请求
            try:
                self._rate_limit_check()
                df = self.pro.index_weight(
                    index_code=index_code,
                    trade_date=test_date,
                    fields=["index_code", "con_code", "trade_date", "weight"]
                )
                success_count += 1
                logger.info(f"Test request {i+1}: Success, got {len(df)} records")
                
            except Exception as e:
                logger.error(f"Test request {i+1}: Failed - {e}")
                break
        
        elapsed_time = time.time() - start_time
        logger.info(f"Test completed: {success_count}/10 requests successful in {elapsed_time:.2f} seconds")
        
        if success_count > 0:
            avg_time_per_request = elapsed_time / success_count
            logger.info(f"Average time per request: {avg_time_per_request:.2f} seconds")

def main():
    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    
    # 初始化数据获取器
    fetcher = IndexWeightFetcher(token)
    
    try:
        # 先测试API限制
        logger.info("Starting API limit test...")
        fetcher.test_api_limits()
        
        # 定义要获取的指数
        index_codes = [
            "932000.CSI",  # 中证2000
            "000300.SH"    # 沪深300
        ]
        
        # 生成月末日期（使用配置中的默认开始年份）
        month_dates = fetcher.get_month_dates()
        logger.info(f"Generated {len(month_dates)} month-end dates: {month_dates}")

        # 获取指数权重数据
        logger.info("Starting index weight data collection...")
        index_weight_data = fetcher.get_index_weight_data(index_codes, month_dates)

        # 保存数据
        timestamp = TimeConfig.get_current_date_str('timestamp')
        filename = f"index_weight_data_{timestamp}"
        fetcher.save_data(index_weight_data, filename)
        
        logger.info("Index weight data collection completed successfully!")
        
        # 显示数据统计
        if not index_weight_data.empty:
            logger.info("\n=== Data Statistics ===")
            for index_code in index_codes:
                index_data = index_weight_data[index_weight_data['index_code'] == index_code]
                avg_stocks = index_data['stock_count'].mean() if not index_data.empty else 0
                logger.info(f"{index_code}: {len(index_data)} records across {len(index_data['trade_date'].unique())} dates, avg {avg_stocks:.0f} stocks per date")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()
