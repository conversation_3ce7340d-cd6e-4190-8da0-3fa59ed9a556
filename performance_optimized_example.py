"""
性能优化示例
展示如何使用优化后的数据获取器实现最佳性能
"""

import logging
import time
from daily_basic_data_fetcher import DailyBasicDataFetcher
from moneyflow_data_fetcher import MoneyflowDataFetcher
from time_config import TimeConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def performance_test_daily_basic():
    """每日基本面数据性能测试"""
    logger.info("=== 每日基本面数据性能测试 ===")
    
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    
    # 使用高性能配置：10线程，启用缓存
    fetcher = DailyBasicDataFetcher(token, max_workers=10)
    
    try:
        # 测试1：多线程批量获取（最快）
        logger.info("--- 测试1：多线程批量获取 ---")
        start_time = time.time()
        
        threaded_data = fetcher.get_daily_basic_data_batch_threaded()
        
        end_time = time.time()
        logger.info(f"多线程批量获取耗时: {end_time - start_time:.2f}秒")
        logger.info(f"获取数据量: {len(threaded_data)} 条记录")
        
        # 测试2：多线程区间获取
        logger.info("--- 测试2：多线程区间获取 ---")
        start_time = time.time()
        
        range_data = fetcher.get_daily_basic_data_range_threaded(
            start_date="20241025",
            end_date="20241031"
        )
        
        end_time = time.time()
        logger.info(f"多线程区间获取耗时: {end_time - start_time:.2f}秒")
        logger.info(f"获取数据量: {len(range_data)} 条记录")
        
        # 测试3：缓存效果测试
        logger.info("--- 测试3：缓存效果测试 ---")
        start_time = time.time()
        
        # 重复获取相同数据，应该命中缓存
        cached_data = fetcher.get_daily_basic_data_range_threaded(
            start_date="20241025",
            end_date="20241031"
        )
        
        end_time = time.time()
        logger.info(f"缓存获取耗时: {end_time - start_time:.2f}秒")
        logger.info(f"缓存数据量: {len(cached_data)} 条记录")
        
        # 显示缓存统计
        cache_stats = fetcher.get_cache_stats()
        logger.info(f"缓存统计: {cache_stats}")
        
        # 获取性能优化建议
        suggestions = fetcher.optimize_performance()
        if suggestions:
            logger.info(f"性能优化建议: {suggestions}")
        
        # 保存数据
        fetcher.save_data(threaded_data, 'performance_daily_basic_threaded')
        fetcher.save_data(range_data, 'performance_daily_basic_range')
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试出错: {e}")
        return False

def performance_test_moneyflow():
    """资金流向数据性能测试"""
    logger.info("=== 资金流向数据性能测试 ===")
    
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    
    # 使用高性能配置：10线程，启用缓存
    fetcher = MoneyflowDataFetcher(token, max_workers=10)
    
    try:
        # 测试1：多线程批量获取
        logger.info("--- 测试1：多线程批量获取 ---")
        start_time = time.time()
        
        threaded_data = fetcher.get_moneyflow_data_batch_threaded()
        
        end_time = time.time()
        logger.info(f"多线程批量获取耗时: {end_time - start_time:.2f}秒")
        logger.info(f"获取数据量: {len(threaded_data)} 条记录")
        
        # 测试2：多线程区间获取
        logger.info("--- 测试2：多线程区间获取 ---")
        start_time = time.time()
        
        range_data = fetcher.get_moneyflow_data_range_threaded(
            start_date="20241025",
            end_date="20241031"
        )
        
        end_time = time.time()
        logger.info(f"多线程区间获取耗时: {end_time - start_time:.2f}秒")
        logger.info(f"获取数据量: {len(range_data)} 条记录")
        
        # 显示缓存统计
        cache_stats = fetcher.get_cache_stats()
        logger.info(f"缓存统计: {cache_stats}")
        
        # 获取性能优化建议
        suggestions = fetcher.optimize_performance()
        if suggestions:
            logger.info(f"性能优化建议: {suggestions}")
        
        # 保存数据
        fetcher.save_data(threaded_data, 'performance_moneyflow_threaded')
        fetcher.save_data(range_data, 'performance_moneyflow_range')
        
        return True
        
    except Exception as e:
        logger.error(f"性能测试出错: {e}")
        return False

def performance_comparison():
    """性能对比测试：串行 vs 多线程"""
    logger.info("=== 性能对比测试 ===")
    
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    
    # 测试数据：最近5个交易日
    trade_dates = TimeConfig.get_recent_trade_dates(5)
    
    # 串行测试
    logger.info("--- 串行获取测试 ---")
    fetcher_serial = DailyBasicDataFetcher(token, max_workers=1)  # 单线程
    fetcher_serial.enable_cache(False)  # 禁用缓存确保公平对比
    
    start_time = time.time()
    serial_data = fetcher_serial.get_daily_basic_data_batch(trade_dates=trade_dates)
    serial_time = time.time() - start_time
    
    logger.info(f"串行获取耗时: {serial_time:.2f}秒")
    logger.info(f"串行数据量: {len(serial_data)} 条记录")
    
    # 多线程测试
    logger.info("--- 多线程获取测试 ---")
    fetcher_parallel = DailyBasicDataFetcher(token, max_workers=10)  # 10线程
    fetcher_parallel.enable_cache(False)  # 禁用缓存确保公平对比
    
    start_time = time.time()
    parallel_data = fetcher_parallel.get_daily_basic_data_batch_threaded(trade_dates=trade_dates)
    parallel_time = time.time() - start_time
    
    logger.info(f"多线程获取耗时: {parallel_time:.2f}秒")
    logger.info(f"多线程数据量: {len(parallel_data)} 条记录")
    
    # 性能提升计算
    if parallel_time > 0:
        speedup = serial_time / parallel_time
        logger.info(f"性能提升: {speedup:.2f}倍")
        logger.info(f"时间节省: {serial_time - parallel_time:.2f}秒 ({((serial_time - parallel_time) / serial_time * 100):.1f}%)")
    
    return {
        'serial_time': serial_time,
        'parallel_time': parallel_time,
        'speedup': speedup if parallel_time > 0 else 0,
        'serial_records': len(serial_data),
        'parallel_records': len(parallel_data)
    }

def main():
    """主函数 - 运行所有性能测试"""
    logger.info("开始性能优化测试")
    
    # 运行每日基本面数据性能测试
    daily_success = performance_test_daily_basic()
    
    # 运行资金流向数据性能测试
    money_success = performance_test_moneyflow()
    
    # 运行性能对比测试
    comparison_results = performance_comparison()
    
    # 总结
    logger.info("=== 性能测试总结 ===")
    logger.info(f"每日基本面测试: {'成功' if daily_success else '失败'}")
    logger.info(f"资金流向测试: {'成功' if money_success else '失败'}")
    logger.info(f"性能提升: {comparison_results.get('speedup', 0):.2f}倍")
    
    logger.info("所有性能测试完成")

if __name__ == "__main__":
    main()
