@echo off
echo ========================================
echo    时间配置管理器启动脚本
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo 检查Flask依赖...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Flask依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: Flask安装失败
        pause
        exit /b 1
    )
    echo Flask安装成功！
) else (
    echo Flask已安装
)

echo.
echo 启动配置管理器...
echo 浏览器将自动打开 http://localhost:5000
echo 按 Ctrl+C 停止服务器
echo.

python config_web_manager.py

pause
