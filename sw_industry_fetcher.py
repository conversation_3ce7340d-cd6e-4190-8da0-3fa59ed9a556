import tushare as ts
import pandas as pd
import time
import logging
import os
from datetime import datetime
from typing import List, Dict, Optional, Any
from time_config import TimeConfig

# 配置日志
os.makedirs('data', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('data', 'sw_industry_fetch.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SWIndustryFetcher:
    def __init__(self, token: str):
        """
        申万行业分类数据获取器
        :param token: tushare token
        """
        self.token = token
        ts.set_token(token)
        self.pro = ts.pro_api()
        
        # 失败请求记录
        self.failed_requests = []
        
        logger.info("SWIndustryFetcher initialized")
    
    def _safe_api_call(self, api_func, params: Dict[str, Any], request_name: str, api_name: str) -> Optional[pd.DataFrame]:
        """
        安全的API调用，包含错误处理和记录
        """
        try:
            TimeConfig.sleep_with_delay('base')  # 使用统一的基础延时
            result = api_func(**params)
            logger.info(f"✓ {request_name}: Success, got {len(result)} records")
            return result

        except Exception as e:
            error_info = {
                'request_name': request_name,
                'api_func_name': api_name,
                'params': params,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.failed_requests.append(error_info)
            logger.error(f"✗ {request_name}: Failed - {e}")

            # 如果是频率限制错误，增加延时
            if any(keyword in str(e).lower() for keyword in ['频率', 'limit', 'rate']):
                logger.warning("Rate limit detected, increasing delay...")
                TimeConfig.sleep_with_delay('rate_limit')

            return None
    
    def get_sw_industry_level1(self) -> pd.DataFrame:
        """
        获取申万一级行业分类
        """
        logger.info("Fetching SW Level 1 industry classification...")

        params = {
            "level": "L1",
            "fields": ["index_code", "industry_name", "level", "industry_code", "is_pub", "parent_code", "src"]
        }

        df = self._safe_api_call(self.pro.index_classify, params, "sw_level1", "index_classify")

        if df is not None and not df.empty:
            logger.info(f"SW Level 1 collection completed: {len(df)} records")
            return df
        else:
            return pd.DataFrame()

    def get_sw_industry_level2(self) -> pd.DataFrame:
        """
        获取申万二级行业分类
        """
        logger.info("Fetching SW Level 2 industry classification...")

        params = {
            "level": "L2",
            "fields": ["index_code", "industry_name", "level", "industry_code", "is_pub", "parent_code", "src"]
        }

        df = self._safe_api_call(self.pro.index_classify, params, "sw_level2", "index_classify")

        if df is not None and not df.empty:
            logger.info(f"SW Level 2 collection completed: {len(df)} records")
            return df
        else:
            return pd.DataFrame()

    def get_sw_industry_level3(self) -> pd.DataFrame:
        """
        获取申万三级行业分类
        """
        logger.info("Fetching SW Level 3 industry classification...")

        params = {
            "level": "L3",
            "fields": ["index_code", "industry_name", "level", "industry_code", "is_pub", "parent_code", "src"]
        }

        df = self._safe_api_call(self.pro.index_classify, params, "sw_level3", "index_classify")

        if df is not None and not df.empty:
            logger.info(f"SW Level 3 collection completed: {len(df)} records")
            return df
        else:
            return pd.DataFrame()
    
    def get_all_sw_stock_classification(self, include_history: bool = False) -> pd.DataFrame:
        """
        获取所有股票的申万行业分类（使用index_member_all接口）
        :param include_history: 是否包含历史分类数据
        """
        logger.info("Fetching all stocks SW industry classification...")

        if include_history:
            # 获取包含历史数据的完整分类
            params = {
                "fields": ["l1_code", "l1_name", "l2_code", "l2_name", "l3_code", "l3_name",
                          "ts_code", "name", "in_date", "out_date", "is_new"]
            }
            request_name = "all_sw_stock_classification_with_history"
        else:
            # 只获取最新分类
            params = {
                "is_new": "Y",
                "fields": ["l1_code", "l1_name", "l2_code", "l2_name", "l3_code", "l3_name",
                          "ts_code", "name", "in_date", "out_date", "is_new"]
            }
            request_name = "all_sw_stock_classification_current"

        df = self._safe_api_call(self.pro.index_member_all, params, request_name, "index_member_all")

        if df is not None and not df.empty:
            logger.info(f"SW stock classification completed: {len(df)} records")
            return df
        else:
            return pd.DataFrame()

    def analyze_classification_changes(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        分析申万分类的历史变化
        :param df: 包含历史数据的分类DataFrame
        """
        logger.info("Analyzing SW classification changes...")

        results = {}

        if df.empty:
            return results

        # 1. 找出有分类变更的股票
        stocks_with_changes = df[df['out_date'].notna()]['ts_code'].unique()
        logger.info(f"Stocks with classification changes: {len(stocks_with_changes)}")

        # 2. 分析每只股票的分类历史
        change_details = []
        for ts_code in stocks_with_changes[:100]:  # 限制分析前100只股票避免过多输出
            stock_history = df[df['ts_code'] == ts_code].sort_values('in_date')
            if len(stock_history) > 1:
                for i in range(len(stock_history)):
                    change_details.append({
                        'ts_code': ts_code,
                        'name': stock_history.iloc[i]['name'],
                        'change_sequence': i + 1,
                        'l1_name': stock_history.iloc[i]['l1_name'],
                        'l2_name': stock_history.iloc[i]['l2_name'],
                        'l3_name': stock_history.iloc[i]['l3_name'],
                        'in_date': stock_history.iloc[i]['in_date'],
                        'out_date': stock_history.iloc[i]['out_date'],
                        'is_current': stock_history.iloc[i]['is_new']
                    })

        if change_details:
            results['classification_changes'] = pd.DataFrame(change_details)

        # 3. 统计最近的分类变更
        recent_changes = df[df['out_date'].notna()].copy()
        if not recent_changes.empty:
            recent_changes['out_date'] = pd.to_datetime(recent_changes['out_date'])
            recent_changes = recent_changes.sort_values('out_date', ascending=False)
            results['recent_changes'] = recent_changes.head(50)  # 最近50个变更

        # 4. 统计各行业的变更频率
        if not recent_changes.empty:
            l1_change_freq = recent_changes.groupby('l1_name').size().reset_index(name='change_count')
            l1_change_freq = l1_change_freq.sort_values('change_count', ascending=False)
            results['l1_change_frequency'] = l1_change_freq

        return results

    def get_sw_industry_list(self) -> pd.DataFrame:
        """
        获取申万行业分类列表（通过index_classify接口）
        """
        logger.info("Fetching SW industry list...")

        params = {
            "fields": ["index_code", "industry_name", "level", "industry_code", "is_pub", "parent_code", "src"]
        }

        df = self._safe_api_call(self.pro.index_classify, params, "sw_industry_list", "index_classify")

        if df is not None and not df.empty:
            logger.info(f"SW industry list completed: {len(df)} records")
            return df
        else:
            return pd.DataFrame()
    
    def get_all_sw_data(self, include_history: bool = False) -> Dict[str, pd.DataFrame]:
        """
        获取所有申万行业分类数据
        :param include_history: 是否包含历史变更数据
        """
        logger.info("Starting comprehensive SW industry data collection...")

        results = {}

        # 1. 获取所有股票的申万分类
        logger.info("\n--- Phase 1: All Stocks SW Classification ---")
        all_stock_classification = self.get_all_sw_stock_classification(include_history=include_history)
        results['all_stock_sw_classification'] = all_stock_classification

        # 2. 获取申万行业列表
        logger.info("\n--- Phase 2: SW Industry List ---")
        industry_list = self.get_sw_industry_list()
        results['sw_industry_list'] = industry_list

        # 3. 按级别分离股票分类数据
        if not all_stock_classification.empty:
            logger.info("\n--- Phase 3: Analyzing classification data ---")

            # 只分析当前有效的分类（is_new='Y'）
            current_classification = all_stock_classification[all_stock_classification['is_new'] == 'Y']

            # 统计各级别的行业数量
            l1_industries = current_classification[['l1_code', 'l1_name']].drop_duplicates()
            l2_industries = current_classification[['l2_code', 'l2_name']].drop_duplicates()
            l3_industries = current_classification[['l3_code', 'l3_name']].drop_duplicates()

            results['sw_l1_industries'] = l1_industries
            results['sw_l2_industries'] = l2_industries
            results['sw_l3_industries'] = l3_industries

            logger.info(f"Level 1 industries: {len(l1_industries)}")
            logger.info(f"Level 2 industries: {len(l2_industries)}")
            logger.info(f"Level 3 industries: {len(l3_industries)}")
            logger.info(f"Total stocks with current SW classification: {len(current_classification)}")

            # 统计每个一级行业的股票数量
            l1_stock_count = current_classification.groupby(['l1_code', 'l1_name']).size().reset_index(name='stock_count')
            results['sw_l1_stock_count'] = l1_stock_count
            logger.info(f"Top 5 L1 industries by stock count:")
            for _, row in l1_stock_count.nlargest(5, 'stock_count').iterrows():
                logger.info(f"  {row['l1_name']}: {row['stock_count']} stocks")

        # 4. 如果包含历史数据，分析变更情况
        if include_history and not all_stock_classification.empty:
            logger.info("\n--- Phase 4: Analyzing Historical Changes ---")
            change_analysis = self.analyze_classification_changes(all_stock_classification)
            results.update(change_analysis)

            if 'classification_changes' in change_analysis:
                logger.info(f"Found {len(change_analysis['classification_changes'])} classification change records")

            if 'recent_changes' in change_analysis:
                logger.info(f"Recent changes: {len(change_analysis['recent_changes'])} records")
                logger.info("Most recent changes:")
                for _, row in change_analysis['recent_changes'].head(5).iterrows():
                    logger.info(f"  {row['ts_code']} - {row['name']}: {row['l1_name']} (out: {row['out_date']})")

        return results
    
    def save_data(self, data: pd.DataFrame, filename: str):
        """保存数据到CSV文件"""
        if not data.empty:
            os.makedirs('data', exist_ok=True)
            filepath = os.path.join('data', f"{filename}.csv")
            
            data.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"Data saved to {filepath}")
            logger.info(f"Data shape: {data.shape}")
        else:
            logger.warning(f"No data to save for {filename}")
    
    def save_failed_requests_log(self):
        """保存失败请求日志"""
        if self.failed_requests:
            os.makedirs('data', exist_ok=True)
            timestamp = TimeConfig.get_current_date_str('timestamp')
            log_file = os.path.join('data', f'sw_failed_requests_{timestamp}.json')
            
            import json
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_requests, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Failed requests log saved to {log_file}")

def main():
    # 使用提供的token
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    
    # 初始化申万行业数据获取器
    fetcher = SWIndustryFetcher(token)
    
    try:
        logger.info("=== Starting SW Industry Data Collection ===")
        
        # 获取所有申万行业数据（包含历史变更）
        all_sw_data = fetcher.get_all_sw_data(include_history=True)

        # 保存所有数据
        timestamp = TimeConfig.get_current_date_str('timestamp')
        for data_name, data_df in all_sw_data.items():
            filename = f"{data_name}_{timestamp}"
            fetcher.save_data(data_df, filename)
        
        # 保存失败请求日志
        fetcher.save_failed_requests_log()
        
        logger.info("=== SW Industry Data Collection Completed ===")
        if fetcher.failed_requests:
            logger.warning(f"Still have {len(fetcher.failed_requests)} failed requests")
        else:
            logger.info("All requests completed successfully!")
        
        # 显示数据统计
        logger.info("\n=== Data Statistics ===")
        for data_name, data_df in all_sw_data.items():
            if not data_df.empty:
                logger.info(f"{data_name}: {len(data_df)} records")

        # 特别分析股票覆盖情况
        if 'all_stock_sw_classification' in all_sw_data and not all_sw_data['all_stock_sw_classification'].empty:
            sw_stocks = set(all_sw_data['all_stock_sw_classification']['ts_code'].unique())
            logger.info(f"\n=== Coverage Analysis ===")
            logger.info(f"Stocks with SW classification: {len(sw_stocks)}")

            # 如果有股票基本信息，对比覆盖率
            try:
                all_stocks = fetcher.pro.stock_basic(exchange='', list_status='L', fields=['ts_code'])
                total_stocks = set(all_stocks['ts_code'].tolist())
                coverage_rate = len(sw_stocks) / len(total_stocks) * 100
                logger.info(f"Total listed stocks: {len(total_stocks)}")
                logger.info(f"SW classification coverage: {coverage_rate:.1f}%")

                # 找出没有申万分类的股票
                no_sw_stocks = total_stocks - sw_stocks
                logger.info(f"Stocks without SW classification: {len(no_sw_stocks)}")
                if no_sw_stocks:
                    logger.info("Examples of stocks without SW classification:")
                    for stock in list(no_sw_stocks)[:10]:
                        logger.info(f"  {stock}")

            except Exception as e:
                logger.warning(f"Could not analyze coverage: {e}")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        fetcher.save_failed_requests_log()
        raise

if __name__ == "__main__":
    main()
