"""
统一的时间配置管理模块
集中管理所有时间相关的设置，便于统一调整和维护
"""

import calendar
import time
from datetime import datetime, timedelta
from typing import List, Optional


class TimeConfig:
    """时间配置管理类"""
    
    # ==================== 时间范围设置 ====================
    
    # 数据获取的默认开始年份
    DEFAULT_START_YEAR = 2024
    
    # 报告期设置
    REPORT_PERIODS_YEARS_BACK = 1  # 获取最近几年的报告期
    QUARTERLY_DATES = ["0331", "0630", "0930", "1231"]  # 季报日期
    
    # 交易日设置
    DEFAULT_TRADE_DAYS_COUNT = 360  # 默认获取最近交易日数量

    # 年度数据获取设置
    ANNUAL_DATA_YEAR = 2023  # 年度数据获取的目标年份
    ENABLE_ANNUAL_MODE = True  # 是否启用年度数据获取模式
    
    # ==================== 延时和速率控制 ====================
    
    # API请求延时设置（秒）
    BASE_DELAY = 0.2  # 基础延时200ms
    QUICK_DELAY = 0.155  # 快速延时100ms
    RATE_LIMIT_DELAY = 5  # 频率限制时的延时
    API_ERROR_DELAY = 60  # API错误时的延时
    
    # 速率限制设置
    MAX_REQUESTS_PER_MINUTE = 100  # 每分钟最大请求数
    RATE_LIMIT_WINDOW = 60  # 速率限制时间窗口（秒）
    
    # ==================== 日期格式设置 ====================
    
    # 日期格式
    DATE_FORMAT_YYYYMMDD = '%Y%m%d'
    DATE_FORMAT_DISPLAY = '%Y/%m/%d'
    TIMESTAMP_FORMAT = '%Y%m%d_%H%M%S'
    LOG_TIME_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # ==================== 测试设置 ====================
    
    # 测试用的固定日期
    TEST_DATE = "20241031"
    
    @classmethod
    def get_current_date_str(cls, format_type: str = 'yyyymmdd') -> str:
        """
        获取当前日期字符串
        :param format_type: 格式类型 'yyyymmdd', 'display', 'timestamp'
        """
        now = datetime.now()
        if format_type == 'yyyymmdd':
            return now.strftime(cls.DATE_FORMAT_YYYYMMDD)
        elif format_type == 'display':
            return now.strftime(cls.DATE_FORMAT_DISPLAY)
        elif format_type == 'timestamp':
            return now.strftime(cls.TIMESTAMP_FORMAT)
        else:
            return now.strftime(cls.DATE_FORMAT_YYYYMMDD)
    
    @classmethod
    def get_month_end_dates(cls, start_year: Optional[int] = None, end_date: Optional[str] = None) -> List[str]:
        """
        生成从指定年份到当前的每月最后一个交易日列表
        :param start_year: 开始年份，默认使用DEFAULT_START_YEAR
        :param end_date: 结束日期，格式YYYYMMDD，默认为当前日期
        """
        if start_year is None:
            start_year = cls.DEFAULT_START_YEAR
            
        if end_date is None:
            end_date = cls.get_current_date_str()
        
        end_year = int(end_date[:4])
        end_month = int(end_date[4:6])
        
        month_dates = []
        
        for year in range(start_year, end_year + 1):
            start_month = 1 if year > start_year else 1
            final_month = 12 if year < end_year else end_month
            
            for month in range(start_month, final_month + 1):
                # 获取该月最后一天
                last_day = calendar.monthrange(year, month)[1]
                month_end = f"{year:04d}{month:02d}{last_day:02d}"
                month_dates.append(month_end)
        
        return month_dates
    
    @classmethod
    def get_quarterly_periods(cls, years_back: Optional[int] = None) -> List[str]:
        """
        生成报告期列表 (YYYYMMDD格式)
        :param years_back: 往前几年，默认使用REPORT_PERIODS_YEARS_BACK
        """
        if years_back is None:
            years_back = cls.REPORT_PERIODS_YEARS_BACK
            
        periods = []
        current_year = datetime.now().year
        
        # 生成指定年数的季报期
        for year in range(current_year - years_back, current_year + 1):
            for quarter_date in cls.QUARTERLY_DATES:
                periods.append(f"{year}{quarter_date}")
        
        # 只返回已经过去的报告期
        current_date = cls.get_current_date_str()
        return [p for p in periods if p <= current_date]
    
    @classmethod
    def get_recent_trade_dates(cls, days: Optional[int] = None) -> List[str]:
        """
        获取最近的交易日期（简单过滤周末）
        :param days: 天数，默认使用DEFAULT_TRADE_DAYS_COUNT
        """
        # 如果启用年度模式，返回年度交易日
        if cls.ENABLE_ANNUAL_MODE:
            return cls.get_annual_trade_dates(cls.ANNUAL_DATA_YEAR)

        if days is None:
            days = cls.DEFAULT_TRADE_DAYS_COUNT

        dates = []
        current_date = datetime.now()

        for i in range(days * 2):  # 多取一些日期以确保有足够的交易日
            date_obj = current_date - timedelta(days=i)
            date_str = date_obj.strftime(cls.DATE_FORMAT_YYYYMMDD)
            # 简单过滤周末
            weekday = date_obj.weekday()
            if weekday < 5:  # 周一到周五
                dates.append(date_str)
            if len(dates) >= days:
                break

        return dates[:days]

    @classmethod
    def get_annual_trade_dates(cls, year: int) -> List[str]:
        """
        获取指定年份的所有交易日期
        :param year: 年份
        """
        dates = []
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31)

        current_date = start_date
        while current_date <= end_date:
            # 简单过滤周末
            if current_date.weekday() < 5:  # 周一到周五
                date_str = current_date.strftime(cls.DATE_FORMAT_YYYYMMDD)
                dates.append(date_str)
            current_date += timedelta(days=1)

        return dates

    @classmethod
    def get_annual_trade_dates_count(cls, year: int) -> int:
        """
        获取指定年份的交易日数量
        :param year: 年份
        """
        return len(cls.get_annual_trade_dates(year))
    
    @classmethod
    def sleep_with_delay(cls, delay_type: str = 'base'):
        """
        根据延时类型进行等待
        :param delay_type: 延时类型 'base', 'quick', 'rate_limit', 'api_error'
        """
        if delay_type == 'base':
            time.sleep(cls.BASE_DELAY)
        elif delay_type == 'quick':
            time.sleep(cls.QUICK_DELAY)
        elif delay_type == 'rate_limit':
            time.sleep(cls.RATE_LIMIT_DELAY)
        elif delay_type == 'api_error':
            time.sleep(cls.API_ERROR_DELAY)
        else:
            time.sleep(cls.BASE_DELAY)
    
    @classmethod
    def format_date_for_display(cls, date_str: str) -> str:
        """
        将YYYYMMDD格式的日期转换为显示格式
        :param date_str: YYYYMMDD格式的日期字符串
        """
        try:
            date_obj = datetime.strptime(date_str, cls.DATE_FORMAT_YYYYMMDD)
            return date_obj.strftime(cls.DATE_FORMAT_DISPLAY)
        except ValueError:
            return date_str  # 如果转换失败，返回原字符串


class RateLimitManager:
    """速率限制管理器"""
    
    def __init__(self, max_requests: Optional[int] = None, window_seconds: Optional[int] = None):
        """
        初始化速率限制管理器
        :param max_requests: 最大请求数，默认使用TimeConfig.MAX_REQUESTS_PER_MINUTE
        :param window_seconds: 时间窗口，默认使用TimeConfig.RATE_LIMIT_WINDOW
        """
        self.max_requests = max_requests or TimeConfig.MAX_REQUESTS_PER_MINUTE
        self.window_seconds = window_seconds or TimeConfig.RATE_LIMIT_WINDOW
        self.request_times = []
    
    def check_and_wait(self):
        """检查并控制请求速率"""
        current_time = time.time()
        
        # 移除时间窗口外的请求记录
        self.request_times = [t for t in self.request_times if current_time - t < self.window_seconds]
        
        # 如果请求数量达到限制，等待
        if len(self.request_times) >= self.max_requests:
            sleep_time = self.window_seconds - (current_time - self.request_times[0]) + 1
            if sleep_time > 0:
                time.sleep(sleep_time)
                return self.check_and_wait()
        
        # 记录当前请求时间
        self.request_times.append(current_time)
        
        # 添加基础延时
        TimeConfig.sleep_with_delay('quick')


# 创建全局配置实例
time_config = TimeConfig()
