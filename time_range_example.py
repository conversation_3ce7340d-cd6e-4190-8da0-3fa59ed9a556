"""
时间区间数据获取示例
展示如何使用时间区间功能获取指定日期范围的数据
"""

import logging
from daily_basic_data_fetcher import DailyBasicDataFetcher
from moneyflow_data_fetcher import MoneyflowDataFetcher

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def example_time_range_daily_basic():
    """每日基本面数据时间区间示例"""
    logger.info("=== 每日基本面数据时间区间示例 ===")
    
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    fetcher = DailyBasicDataFetcher(token, max_workers=10)
    
    try:
        # 示例1：获取指定时间区间的数据（串行）
        logger.info("--- 示例1：串行获取时间区间数据 ---")
        range_data = fetcher.get_daily_basic_data_range(
            start_date="20241001",  # 开始日期
            end_date="20241031"     # 结束日期
        )
        fetcher.save_data(range_data, "daily_basic_range_serial")
        logger.info(f"串行获取完成，数据量: {len(range_data)} 条")
        
        # 示例2：获取指定时间区间的数据（多线程，推荐）
        logger.info("--- 示例2：多线程获取时间区间数据 ---")
        range_data_threaded = fetcher.get_daily_basic_data_range_threaded(
            start_date="20241001",  # 开始日期
            end_date="20241031"     # 结束日期
        )
        fetcher.save_data(range_data_threaded, "daily_basic_range_threaded")
        logger.info(f"多线程获取完成，数据量: {len(range_data_threaded)} 条")
        
        # 示例3：自定义字段的时间区间数据
        logger.info("--- 示例3：自定义字段的时间区间数据 ---")
        custom_fields = ["ts_code", "trade_date", "pe", "pb", "total_mv", "circ_mv"]
        custom_range_data = fetcher.get_daily_basic_data_range_threaded(
            start_date="20241025",  # 开始日期
            end_date="20241031",    # 结束日期
            fields=custom_fields    # 自定义字段
        )
        fetcher.save_data(custom_range_data, "daily_basic_range_custom")
        logger.info(f"自定义字段获取完成，数据量: {len(custom_range_data)} 条")
        
        # 显示缓存统计
        cache_stats = fetcher.get_cache_stats()
        logger.info(f"缓存统计: {cache_stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"每日基本面数据获取出错: {e}")
        return False

def example_time_range_moneyflow():
    """资金流向数据时间区间示例"""
    logger.info("=== 资金流向数据时间区间示例 ===")
    
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    fetcher = MoneyflowDataFetcher(token, max_workers=10)
    
    try:
        # 示例1：获取指定时间区间的数据（串行）
        logger.info("--- 示例1：串行获取时间区间数据 ---")
        range_data = fetcher.get_moneyflow_data_range(
            start_date="20241001",  # 开始日期
            end_date="20241031"     # 结束日期
        )
        fetcher.save_data(range_data, "moneyflow_range_serial")
        logger.info(f"串行获取完成，数据量: {len(range_data)} 条")
        
        # 示例2：获取指定时间区间的数据（多线程，推荐）
        logger.info("--- 示例2：多线程获取时间区间数据 ---")
        range_data_threaded = fetcher.get_moneyflow_data_range_threaded(
            start_date="20241001",  # 开始日期
            end_date="20241031"     # 结束日期
        )
        fetcher.save_data(range_data_threaded, "moneyflow_range_threaded")
        logger.info(f"多线程获取完成，数据量: {len(range_data_threaded)} 条")
        
        # 示例3：自定义字段的时间区间数据
        logger.info("--- 示例3：自定义字段的时间区间数据 ---")
        custom_fields = [
            "ts_code", "trade_date", "buy_sm_amount", "sell_sm_amount",
            "buy_lg_amount", "sell_lg_amount", "net_mf_amount"
        ]
        custom_range_data = fetcher.get_moneyflow_data_range_threaded(
            start_date="20241025",  # 开始日期
            end_date="20241031",    # 结束日期
            fields=custom_fields    # 自定义字段
        )
        fetcher.save_data(custom_range_data, "moneyflow_range_custom")
        logger.info(f"自定义字段获取完成，数据量: {len(custom_range_data)} 条")
        
        # 显示缓存统计
        cache_stats = fetcher.get_cache_stats()
        logger.info(f"缓存统计: {cache_stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"资金流向数据获取出错: {e}")
        return False

def example_flexible_time_ranges():
    """灵活的时间区间示例"""
    logger.info("=== 灵活的时间区间示例 ===")
    
    token = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
    daily_fetcher = DailyBasicDataFetcher(token, max_workers=10)
    money_fetcher = MoneyflowDataFetcher(token, max_workers=10)
    
    # 不同的时间区间示例
    time_ranges = [
        ("20241001", "20241007", "第一周"),
        ("20241008", "20241014", "第二周"),
        ("20241015", "20241021", "第三周"),
        ("20241022", "20241031", "第四周")
    ]
    
    for start_date, end_date, description in time_ranges:
        logger.info(f"--- 获取{description}数据: {start_date} 到 {end_date} ---")
        
        # 获取每日基本面数据
        daily_data = daily_fetcher.get_daily_basic_data_range_threaded(
            start_date=start_date,
            end_date=end_date
        )
        
        # 获取资金流向数据
        money_data = money_fetcher.get_moneyflow_data_range_threaded(
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"{description} - 基本面数据: {len(daily_data)} 条，资金流向数据: {len(money_data)} 条")

def main():
    """主函数 - 运行时间区间示例"""
    logger.info("开始运行时间区间数据获取示例")
    
    # 运行每日基本面数据时间区间示例
    daily_success = example_time_range_daily_basic()
    
    # 运行资金流向数据时间区间示例
    money_success = example_time_range_moneyflow()
    
    # 运行灵活时间区间示例
    example_flexible_time_ranges()
    
    # 总结
    logger.info("=== 时间区间示例总结 ===")
    logger.info(f"每日基本面时间区间示例: {'成功' if daily_success else '失败'}")
    logger.info(f"资金流向时间区间示例: {'成功' if money_success else '失败'}")
    
    logger.info("时间区间数据获取示例完成")
    logger.info("可用的时间区间方法:")
    logger.info("1. get_daily_basic_data_range() - 串行获取每日基本面区间数据")
    logger.info("2. get_daily_basic_data_range_threaded() - 多线程获取每日基本面区间数据")
    logger.info("3. get_moneyflow_data_range() - 串行获取资金流向区间数据")
    logger.info("4. get_moneyflow_data_range_threaded() - 多线程获取资金流向区间数据")

if __name__ == "__main__":
    main()
