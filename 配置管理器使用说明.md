# 🕒 时间配置管理器使用说明

## 📋 功能介绍

这是一个Web界面的时间配置管理工具，让您可以通过浏览器方便地修改所有时间相关的配置，无需手动编辑代码文件。

## 🚀 快速开始

### 方法一：使用批处理文件（推荐）
1. 双击运行 `start_config_manager.bat`
2. 脚本会自动检查并安装依赖
3. 浏览器会自动打开配置界面

### 方法二：手动启动
1. 安装依赖：`pip install -r requirements.txt`
2. 运行：`python config_web_manager.py`
3. 打开浏览器访问：http://localhost:5000

## 🎛️ 配置项说明

### 📅 时间范围设置
- **默认开始年份**：数据获取的起始年份（如2024）
- **报告期回溯年数**：获取最近几年的季报数据（如1表示最近1年）
- **默认交易日数量**：获取最近交易日的数量（如10个交易日）

### ⏱️ 延时设置
- **基础延时**：API请求的基础等待时间（秒，如0.2）
- **快速延时**：快速请求的等待时间（秒，如0.1）
- **频率限制延时**：触发频率限制时的等待时间（秒，如5）
- **API错误延时**：API出错时的等待时间（秒，如60）

### 🚦 速率限制设置
- **每分钟最大请求数**：防止API限制的请求频率控制（如100）
- **速率限制时间窗口**：速率统计的时间窗口（秒，如60）

### 🧪 测试设置
- **测试日期**：用于测试的固定日期（YYYYMMDD格式，如20241031）

## 🔧 功能按钮

- **💾 保存配置**：将修改保存到配置文件
- **🔄 恢复备份**：恢复到上次保存前的配置
- **🔄 刷新配置**：重新加载当前配置（用于查看最新状态）

## ⚠️ 注意事项

1. **自动备份**：每次保存配置前会自动备份原配置文件
2. **实时生效**：配置保存后，新启动的程序会使用新配置
3. **安全性**：建议在修改重要配置前先测试
4. **数值范围**：请确保输入的数值在合理范围内

## 🔍 配置文件位置

- **主配置文件**：`time_config.py`
- **备份文件**：`time_config_backup.py`
- **Web管理器**：`config_web_manager.py`

## 🛠️ 故障排除

### 问题：浏览器无法打开
- 检查是否有其他程序占用5000端口
- 尝试手动访问 http://localhost:5000

### 问题：配置保存失败
- 检查文件权限
- 确保 `time_config.py` 文件存在且可写

### 问题：Flask依赖错误
- 运行：`pip install Flask==2.3.3`
- 或使用：`pip install -r requirements.txt`

## 📞 技术支持

如果遇到问题，请检查：
1. Python环境是否正确安装
2. 所有文件是否在同一目录
3. 网络连接是否正常
4. 防火墙是否阻止了本地服务器

---

**提示**：修改配置后，建议先用小范围数据测试，确认无误后再进行大规模数据处理。
